import { useState, useCallback, useRef, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '@/store/store';
import {
  setGraphMemorySuccess,
  setGraphMemoryNodesAndEdges,
  setGraphMemoryLoading,
  setStats,
  selectNode,
  deselectNode,
  selectEdge,
  deselectEdge,
  clearSelection,
  updateFilters,
  updateViewState,
  addHistoryItem,
  setGraphMemoryError
} from '@/store/graphMemorySlice';
import { realMem0Client } from '@/lib/mem0-client/realClient';
import { transformMem0GraphToReactFlow, transformReactFlowToMem0Graph, validateGraphData } from '@/lib/graph-data-transformer';
import {
  Mem0GraphMemoryResponse,
  Mem0GraphEntity,
  Mem0GraphRelation,
  GraphEntityCreateRequest,
  GraphRelationCreateRequest,
  GraphBatchUpdateRequest,
  Mem0ApiError
} from '@/types/mem0-api';
import {
  GraphNode,
  GraphEdge,
  GraphMemoryFilters,
  GraphMemoryViewState,
  GraphMemoryStats
} from '@/types/graph-memory';

// 缓存条目接口
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  key: string;
}

// 分页参数接口
interface PaginationParams {
  limit?: number;
  offset?: number;
  hasMore?: boolean;
}

// Graph Memory API Hook 返回类型
interface UseGraphMemoryApiReturn {
  // 数据获取
  fetchGraphMemories: (filters?: GraphMemoryFilters) => Promise<{ nodes: GraphNode[]; edges: GraphEdge[] }>;
  fetchGraphStats: (filters?: GraphMemoryFilters) => Promise<GraphMemoryStats>;

  // 实体管理
  createEntity: (entity: Omit<GraphEntityCreateRequest, 'user_id'>) => Promise<Mem0GraphEntity>;
  updateEntity: (entityId: string, updates: Partial<Mem0GraphEntity>) => Promise<Mem0GraphEntity>;
  deleteEntity: (entityId: string) => Promise<void>;

  // 关系管理
  createRelation: (relation: Omit<GraphRelationCreateRequest, 'user_id'>) => Promise<Mem0GraphRelation>;
  updateRelation: (relationId: string, updates: Partial<Mem0GraphRelation>) => Promise<Mem0GraphRelation>;

  // 批量操作
  deleteEntities: (entityIds: string[]) => Promise<void>;
  deleteRelations: (relationIds: string[]) => Promise<void>;
  batchUpdateEntities: (entityIds: string[], updates: Partial<Mem0GraphEntity>) => Promise<Mem0GraphEntity[]>;
  batchUpdateRelations: (relationIds: string[], updates: Partial<Mem0GraphRelation>) => Promise<Mem0GraphRelation[]>;
  exportGraphData: (data?: { nodes: GraphNode[]; edges: GraphEdge[] }) => Promise<void>;
  deleteRelation: (relationId: string) => Promise<void>;

  // 视图状态管理
  updateViewState: (viewState: Partial<GraphMemoryViewState>) => void;
  updateFilters: (filters: Partial<GraphMemoryFilters>) => void;

  // 数据验证
  validateGraphData: (nodes: GraphNode[], edges: GraphEdge[]) => { isValid: boolean; errors: string[] };

  // 状态
  isLoading: boolean;
  error: string | null;
  hasUpdates: number;

  // Redux状态
  nodes: GraphNode[];
  edges: GraphEdge[];
  stats: GraphMemoryStats | null;
  filters: GraphMemoryFilters;
  viewState: GraphMemoryViewState;
}

export const useGraphMemoryApi = (): UseGraphMemoryApiReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [hasUpdates, setHasUpdates] = useState<number>(0);

  const dispatch = useDispatch<AppDispatch>();
  const user_id = useSelector((state: RootState) => state.profile.userId);

  // 缓存管理
  const cacheRef = useRef<Map<string, CacheEntry<unknown>>>(new Map());
  const CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存时间
  const MAX_CACHE_SIZE = 50; // 最大缓存条目数

  // 分页状态
  const [pagination, setPagination] = useState<PaginationParams>({
    limit: 100,
    offset: 0,
    hasMore: true
  });

  // 缓存辅助函数
  const getCacheKey = useCallback((prefix: string, params: Record<string, unknown>): string => {
    return `${prefix}_${JSON.stringify(params)}`;
  }, []);

  const getFromCache = useCallback(<T>(key: string): T | null => {
    const entry = cacheRef.current.get(key);
    if (!entry) return null;

    // 检查缓存是否过期
    if (Date.now() - entry.timestamp > CACHE_TTL) {
      cacheRef.current.delete(key);
      return null;
    }

    return entry.data as T;
  }, []);

  const setToCache = useCallback(<T>(key: string, data: T): void => {
    // 如果缓存已满，删除最旧的条目
    if (cacheRef.current.size >= MAX_CACHE_SIZE) {
      const oldestKey = cacheRef.current.keys().next().value;
      if (oldestKey) {
        cacheRef.current.delete(oldestKey);
      }
    }

    cacheRef.current.set(key, {
      data,
      timestamp: Date.now(),
      key
    });
  }, []);

  const clearCache = useCallback((prefix?: string): void => {
    if (prefix) {
      // 清除特定前缀的缓存
      for (const [key] of cacheRef.current) {
        if (key.startsWith(prefix)) {
          cacheRef.current.delete(key);
        }
      }
    } else {
      // 清除所有缓存
      cacheRef.current.clear();
    }
  }, []);
  
  // Redux状态选择器
  const {
    nodes,
    edges,
    stats,
    selectedNodeIds,
    selectedEdgeIds,
    filters,
    viewState
  } = useSelector((state: RootState) => state.graphMemory);

  // 错误处理辅助函数
  const handleError = useCallback((err: unknown, defaultMessage: string) => {
    const errorMessage = (err as Mem0ApiError).message || (err as Error).message || defaultMessage;
    setGraphMemoryError(errorMessage);
    dispatch(setGraphMemoryError(errorMessage));
    // Error status will be set by setGraphMemoryError
    setIsLoading(false);
    throw new Error(errorMessage);
  }, [dispatch]);

  // 数据获取（支持缓存和分页）
  const fetchGraphMemories = useCallback(async (
    filters?: GraphMemoryFilters,
    options?: { useCache?: boolean; loadMore?: boolean }
  ): Promise<{ nodes: GraphNode[]; edges: GraphEdge[] }> => {
    const { useCache = true, loadMore = false } = options || {};

    // 构建请求参数
    const params = {
      user_id,
      ...(filters?.entity_types && { entity_types: filters.entity_types }),
      ...(filters?.relation_types && { relation_types: filters.relation_types }),
      ...(filters?.agent_id && { agent_id: filters.agent_id }),
      ...(filters?.run_id && { run_id: filters.run_id }),
      ...(filters?.entity_search && { entity_search: filters.entity_search }),
      // relation_search 字段暂时注释掉，因为类型定义中不存在
      ...(filters?.date_range && {
        start_date: filters.date_range.start,
        end_date: filters.date_range.end
      }),
      limit: pagination.limit,
      offset: loadMore ? pagination.offset : 0
    };

    // 检查缓存
    const cacheKey = getCacheKey('graph_memories', params);
    if (useCache && !loadMore) {
      const cachedData = getFromCache<{ nodes: GraphNode[]; edges: GraphEdge[] }>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    setIsLoading(true);
    dispatch(setGraphMemoryLoading());

    try {
      const response = await realMem0Client.getGraphMemories(params);
      
      // 转换数据格式
      const { nodes, edges } = transformMem0GraphToReactFlow(
        response,
        viewState.layout || 'force',
        {
          width: 800,
          height: 600,
          nodeSpacing: 150,
          levelSpacing: 200
        }
      );

      // 验证数据
      const validation = validateGraphData(nodes, edges);
      if (!validation.isValid) {
        console.warn('Graph data validation warnings:', validation.errors);
      }

      // 更新Redux状态
      dispatch(setGraphMemoryNodesAndEdges({ nodes, edges }));

      // 缓存数据
      if (useCache) {
        setToCache(cacheKey, { nodes, edges });
      }

      setIsLoading(false);
      setHasUpdates(prev => prev + 1);

      return { nodes, edges };
    } catch (err: unknown) {
      handleError(err, 'Failed to fetch graph memories');
      return { nodes: [], edges: [] };
    }
  }, [user_id, viewState.layout, dispatch, handleError]);

  // 获取图统计信息
  const fetchGraphStats = useCallback(async (
    filters?: GraphMemoryFilters
  ): Promise<GraphMemoryStats> => {
    setIsLoading(true);
    dispatch(setGraphMemoryLoading());
    
    try {
      const params = {
        user_id,
        ...(filters?.entity_types && { entity_types: filters.entity_types }),
        ...(filters?.relation_types && { relation_types: filters.relation_types }),
        ...(filters?.agent_id && { agent_id: filters.agent_id }),
        ...(filters?.run_id && { run_id: filters.run_id })
      };

      const response = await realMem0Client.getGraphStats(params);
      
      const stats: GraphMemoryStats = {
        total_entities: response.total_entities || 0,
        total_relations: response.total_relations || 0,
        graph_density: response.graph_density || 0,
        active_entities: response.active_entities || 0,
        entity_types_count: response.entity_types_count || {},
        relation_types_count: response.relation_types_count || {},
        avg_connections_per_entity: response.avg_connections_per_entity || 0,
        most_connected_entities: response.most_connected_entities || []
      };

      dispatch(setStats(stats));
      setIsLoading(false);
      // Status will be set by setGraphMemorySuccess

      return stats;
    } catch (err: unknown) {
      handleError(err, 'Failed to fetch graph statistics');
      return {
        total_entities: 0,
        total_relations: 0,
        graph_density: 0,
        active_entities: 0,
        entity_types_count: {},
        relation_types_count: {},
        avg_connections_per_entity: 0,
        most_connected_entities: []
      };
    }
  }, [user_id, dispatch, handleError]);

  // 简化版本：移除搜索功能

  // 创建实体
  const createEntity = useCallback(async (
    entity: Omit<GraphEntityCreateRequest, 'user_id'>
  ): Promise<Mem0GraphEntity> => {
    setIsLoading(true);
    dispatch(setGraphMemoryLoading());
    
    try {
      const response = await realMem0Client.createGraphEntity({
        ...entity,
        user_id
      });

      // 记录操作历史
      dispatch(addHistoryItem({
        id: Date.now().toString(),
        operation: 'entity_create',
        timestamp: new Date().toISOString(),
        target_id: response.entity.id,
        target_type: 'entity',
        description: `Created entity: ${entity.name}`,
        metadata: { entity: response.entity }
      }));

      setIsLoading(false);
      // Status will be set by setGraphMemorySuccess
      setHasUpdates(prev => prev + 1);

      return response.entity;
    } catch (err: unknown) {
      handleError(err, 'Failed to create entity');
      throw err;
    }
  }, [user_id, dispatch, handleError]);

  // 更新实体
  const updateEntity = useCallback(async (
    entityId: string,
    updates: Partial<Mem0GraphEntity>
  ): Promise<Mem0GraphEntity> => {
    setIsLoading(true);
    dispatch(setGraphMemoryLoading());
    
    try {
      const response = await realMem0Client.updateGraphEntity(entityId, updates);

      // 记录操作历史
      dispatch(addHistoryItem({
        id: Date.now().toString(),
        operation: 'entity_update',
        timestamp: new Date().toISOString(),
        target_id: entityId,
        target_type: 'entity',
        description: `Updated entity: ${entityId}`,
        changes: { before: updates, after: response.entity },
        metadata: { entityId, updates, result: response.entity }
      }));

      setIsLoading(false);
      // Status will be set by setGraphMemorySuccess
      setHasUpdates(prev => prev + 1);

      return response.entity;
    } catch (err: unknown) {
      handleError(err, 'Failed to update entity');
      throw err;
    }
  }, [dispatch, handleError]);

  // 删除实体
  const deleteEntity = useCallback(async (entityId: string): Promise<void> => {
    setIsLoading(true);
    dispatch(setGraphMemoryLoading());
    
    try {
      await realMem0Client.deleteGraphEntity(entityId);

      // 记录操作历史
      dispatch(addHistoryItem({
        id: Date.now().toString(),
        operation: 'entity_delete',
        timestamp: new Date().toISOString(),
        target_id: entityId,
        target_type: 'entity',
        description: `Deleted entity: ${entityId}`,
        metadata: { entityId }
      }));

      setIsLoading(false);
      // Status will be set by setGraphMemorySuccess
      setHasUpdates(prev => prev + 1);
    } catch (err: unknown) {
      handleError(err, 'Failed to delete entity');
    }
  }, [dispatch, handleError]);

  // 简化版本：移除getEntityRelations函数

  // 创建关系
  const createRelation = useCallback(async (
    relation: Omit<GraphRelationCreateRequest, 'user_id'>
  ): Promise<Mem0GraphRelation> => {
    setIsLoading(true);
    dispatch(setGraphMemoryLoading());
    

    try {
      const response = await realMem0Client.createGraphRelation({
        ...relation,
        user_id
      });

      // 记录操作历史
      dispatch(addHistoryItem({
        id: Date.now().toString(),
        operation: 'relation_create',
        timestamp: new Date().toISOString(),
        target_id: response.relation.id,
        target_type: 'relation',
        description: `Created relation: ${relation.relation_type}`,
        metadata: { relation: response.relation }
      }));

      setIsLoading(false);
      // Status will be set by setGraphMemorySuccess
      setHasUpdates(prev => prev + 1);

      return response.relation;
    } catch (err: unknown) {
      handleError(err, 'Failed to create relation');
      throw err;
    }
  }, [user_id, dispatch, handleError]);

  // 更新关系
  const updateRelation = useCallback(async (
    relationId: string,
    updates: Partial<Mem0GraphRelation>
  ): Promise<Mem0GraphRelation> => {
    setIsLoading(true);
    dispatch(setGraphMemoryLoading());
    

    try {
      const response = await realMem0Client.updateGraphRelation(relationId, updates);

      // 记录操作历史
      dispatch(addHistoryItem({
        id: Date.now().toString(),
        operation: 'relation_update',
        timestamp: new Date().toISOString(),
        target_id: relationId,
        target_type: 'relation',
        description: `Updated relation: ${relationId}`,
        changes: { before: updates, after: response.relation },
        metadata: { relationId, updates, result: response.relation }
      }));

      setIsLoading(false);
      // Status will be set by setGraphMemorySuccess
      setHasUpdates(prev => prev + 1);

      return response.relation;
    } catch (err: unknown) {
      handleError(err, 'Failed to update relation');
      throw err;
    }
  }, [dispatch, handleError]);

  // 删除关系
  const deleteRelation = useCallback(async (relationId: string): Promise<void> => {
    setIsLoading(true);
    dispatch(setGraphMemoryLoading());
    

    try {
      await realMem0Client.deleteGraphRelation(relationId);

      // 记录操作历史
      dispatch(addHistoryItem({
        id: Date.now().toString(),
        operation: 'relation_delete',
        timestamp: new Date().toISOString(),
        target_id: relationId,
        target_type: 'relation',
        description: `Deleted relation: ${relationId}`,
        metadata: { relationId }
      }));

      setIsLoading(false);
      // Status will be set by setGraphMemorySuccess
      setHasUpdates(prev => prev + 1);
    } catch (err: unknown) {
      handleError(err, 'Failed to delete relation');
    }
  }, [dispatch, handleError]);

  // 简化版本：移除复杂的批量操作，专注于核心功能

  // 视图状态管理
  const updateViewStateFunc = useCallback((viewState: Partial<GraphMemoryViewState>) => {
    dispatch(updateViewState(viewState));
  }, [dispatch]);

  const updateFiltersFunc = useCallback((filters: Partial<GraphMemoryFilters>) => {
    dispatch(updateFilters(filters));
  }, [dispatch]);

  // 简化版本：移除选择功能

  // 数据转换和验证
  const validateGraphDataFunc = useCallback((nodes: GraphNode[], edges: GraphEdge[]) => {
    return validateGraphData(nodes, edges);
  }, []);

  const exportGraphData = useCallback(async (data?: { nodes: GraphNode[]; edges: GraphEdge[] }) => {
    try {
      const exportData = data || { nodes, edges };
      const jsonData = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `graph-memory-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
      throw error;
    }
  }, [nodes, edges]);

  // 批量删除实体
  const deleteEntities = useCallback(async (entityIds: string[]): Promise<void> => {
    if (entityIds.length === 0) return;

    setIsLoading(true);
    setError(null);

    try {
      // 并行删除所有实体
      await Promise.all(entityIds.map(id => deleteEntity(id)));

      dispatch(addHistoryItem({
        id: Date.now().toString(),
        operation: 'entity_delete',
        target_type: 'entity',
        target_id: entityIds.join(','),
        description: `Batch deleted ${entityIds.length} entities`,
        timestamp: new Date().toISOString(),
        changes: {
          before: `${entityIds.length} entities`,
          after: '0 entities'
        }
      }));

      setHasUpdates(prev => prev + 1);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete entities';
      setError(errorMessage);
      dispatch(setGraphMemoryError(errorMessage));
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [deleteEntity, dispatch]);

  // 批量删除关系
  const deleteRelations = useCallback(async (relationIds: string[]): Promise<void> => {
    if (relationIds.length === 0) return;

    setIsLoading(true);
    setError(null);

    try {
      // 并行删除所有关系
      await Promise.all(relationIds.map(id => deleteRelation(id)));

      dispatch(addHistoryItem({
        id: Date.now().toString(),
        operation: 'relation_delete',
        target_type: 'relation',
        target_id: relationIds.join(','),
        description: `Batch deleted ${relationIds.length} relations`,
        timestamp: new Date().toISOString(),
        changes: {
          before: `${relationIds.length} relations`,
          after: '0 relations'
        }
      }));

      setHasUpdates(prev => prev + 1);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete relations';
      setError(errorMessage);
      dispatch(setGraphMemoryError(errorMessage));
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [deleteRelation, dispatch]);

  // 批量更新实体
  const batchUpdateEntities = useCallback(async (
    entityIds: string[],
    updates: Partial<Mem0GraphEntity>
  ): Promise<Mem0GraphEntity[]> => {
    if (entityIds.length === 0) return [];

    setIsLoading(true);
    setError(null);

    try {
      // 并行更新所有实体
      const updatedEntities = await Promise.all(
        entityIds.map(id => updateEntity(id, updates))
      );

      dispatch(addHistoryItem({
        id: Date.now().toString(),
        operation: 'entity_update',
        target_type: 'entity',
        target_id: entityIds.join(','),
        description: `Batch updated ${entityIds.length} entities`,
        timestamp: new Date().toISOString(),
        changes: {
          before: `${entityIds.length} entities`,
          after: `Updated ${Object.keys(updates).join(', ')}`
        }
      }));

      setHasUpdates(prev => prev + 1);
      return updatedEntities;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update entities';
      setError(errorMessage);
      dispatch(setGraphMemoryError(errorMessage));
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [updateEntity, dispatch]);

  // 批量更新关系
  const batchUpdateRelations = useCallback(async (
    relationIds: string[],
    updates: Partial<Mem0GraphRelation>
  ): Promise<Mem0GraphRelation[]> => {
    if (relationIds.length === 0) return [];

    setIsLoading(true);
    setError(null);

    try {
      // 并行更新所有关系
      const updatedRelations = await Promise.all(
        relationIds.map(id => updateRelation(id, updates))
      );

      dispatch(addHistoryItem({
        id: Date.now().toString(),
        operation: 'relation_update',
        target_type: 'relation',
        target_id: relationIds.join(','),
        description: `Batch updated ${relationIds.length} relations`,
        timestamp: new Date().toISOString(),
        changes: {
          before: `${relationIds.length} relations`,
          after: `Updated ${Object.keys(updates).join(', ')}`
        }
      }));

      setHasUpdates(prev => prev + 1);
      return updatedRelations;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update relations';
      setError(errorMessage);
      dispatch(setGraphMemoryError(errorMessage));
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [updateRelation, dispatch]);

  // 简化版本：移除复杂的导入功能

  return {
    // 数据获取
    fetchGraphMemories,
    fetchGraphStats,

    // 实体管理
    createEntity,
    updateEntity,
    deleteEntity,

    // 关系管理
    createRelation,
    updateRelation,
    deleteRelation,

    // 批量操作
    deleteEntities,
    deleteRelations,
    batchUpdateEntities,
    batchUpdateRelations,
    exportGraphData,

    // 视图状态管理
    updateViewState: updateViewStateFunc,
    updateFilters: updateFiltersFunc,

    // 数据转换和验证
    validateGraphData: validateGraphDataFunc,

    // 状态
    isLoading,
    error,
    hasUpdates,

    // Redux状态
    nodes,
    edges,
    stats,
    filters,
    viewState
  };
};
